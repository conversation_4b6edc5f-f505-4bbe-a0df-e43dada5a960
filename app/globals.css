@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@keyframes floating {
  0% {
    transform: translateY(4);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(4);
  }
}

.floating {
  animation: floating 2s infinite ease-in-out;
}

/* Disable default cursor */
html, body, a, button {
  cursor: none !important;
}

/* Spline Canvas Styles */
.spline-canvas {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  position: relative !important;
}

.spline-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Custom cursor styles */
body:hover [data-cursor] {
  opacity: 1;
}
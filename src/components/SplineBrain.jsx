'use client';
import { useState } from "react";
import Spline from '@splinetool/react-spline';

const SplineBrain = () => {
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    console.log('Spline scene loaded successfully');
  };

  const handleError = (error) => {
    console.error('Error loading Spline scene:', error);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center text-gray-400">
          {/* Animated Brain Fallback */}
          <div className="relative w-32 h-32 mx-auto mb-4">
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 opacity-20 animate-pulse"></div>
            <div className="absolute inset-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 opacity-30 animate-ping"></div>
            <div className="absolute inset-4 rounded-full bg-gradient-to-r from-purple-300 to-blue-300 opacity-40 animate-bounce"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-4xl animate-pulse">🧠</span>
            </div>
          </div>
          <p className="text-sm font-medium">3D Brain Model</p>
          <p className="text-xs text-gray-500 mt-1">Interactive visualization</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <Spline
        scene="https://prod.spline.design/eZnKlxDS3qFBwkJw/scene.splinecode"
        onLoad={handleLoad}
        onError={handleError}
        style={{
          width: '100%',
          height: '100%',
          background: 'transparent'
        }}
      />
    </div>
  );
};

const SplineBrainCanvas = () => {
  return (
    <div className="w-full h-full min-h-[300px]">
      <SplineBrain />
    </div>
  );
};

export default SplineBrainCanvas;

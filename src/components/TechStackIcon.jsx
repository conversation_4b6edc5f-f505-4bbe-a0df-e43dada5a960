import React from "react";
import Tilt from "react-parallax-tilt";

const TechStackIcon = ({ TechStackIcon, Language }) => {
  return (
    <Tilt
      glareEnable={true}
      glareMaxOpacity={0.2}
      glareColor="#ffffff"
      glarePosition="bottom"
      tiltMaxAngleX={10}
      tiltMaxAngleY={10}
      transitionSpeed={500}
    >
      <div className="group p-6 rounded-2xl bg-slate-800/50 hover:bg-slate-700/50 transition-all duration-300 ease-in-out flex flex-col items-center justify-center gap-3 hover:scale-105 shadow-lg hover:shadow-xl floating">
        <div className="relative">
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-0 group-hover:opacity-50 blur transition duration-300"></div>
          <img 
            src={TechStackIcon} 
            alt={`${Language} icon`} 
            className="relative h-16 w-16 md:h-20 md:w-20 transform transition-transform duration-300"
          />
        </div>
        <span className="text-slate-300 font-semibold text-sm md:text-base tracking-wide group-hover:text-white transition-colors duration-300">
          {Language}
        </span>
      </div>
    </Tilt>
  );
};

export default TechStackIcon;
